"use client"

import { useState, useEffect } from 'react'
import { QuickDomainSetup } from '@/components/admin/quick-domain-setup'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { useToast } from '@/hooks/use-toast'
import { Copy, ExternalLink, CheckCircle, AlertCircle, Globe, Settings, Trash2, Edit, Plus, Search, Clock, XCircle, Shield, RefreshCw } from 'lucide-react'
import Link from 'next/link'


interface TenantData {
  id: string
  name: string
  slug: string
  domain: string | null
  subscriptionPlan: string
  subscriptionStatus: string
  createdAt: string
}

interface CustomDomainRequest {
  id: string
  domain: string
  status: 'pending' | 'approved' | 'rejected' | 'configuration_failed'
  requested_at: string
}

interface PlatformConfig {
  domain: string
  protocol: string
  subdomainPattern: string
}

interface DomainStatus {
  domain: string
  verified: boolean
  ssl: boolean
  lastChecked: string
}

export default function AdminDomainsPage() {
  const [tenants, setTenants] = useState<TenantData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedTenant, setSelectedTenant] = useState<TenantData | null>(null)
  const [newDomain, setNewDomain] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [platformConfig, setPlatformConfig] = useState<PlatformConfig | null>(null)
  const [domainStatus, setDomainStatus] = useState<DomainStatus | null>(null)
  const [pendingRequests, setPendingRequests] = useState<CustomDomainRequest[]>([])
  const { toast } = useToast()

  useEffect(() => {
    fetchTenants()
    fetchPlatformConfig()
    fetchPendingRequests()
  }, [])

  const fetchPlatformConfig = async () => {
    try {
      const response = await fetch('/api/platform/config')
      const data = await response.json()

      if (data.success && data.config) {
        setPlatformConfig(data.config)
        console.log('🔥 ADMIN: Platform config loaded:', data.config)
      } else {
        console.error('🔥 ADMIN: Failed to fetch platform config:', data.error)
        // Set fallback config
        setPlatformConfig({
          domain: 'sellzio.com',
          protocol: 'https',
          subdomainPattern: '*.sellzio.com'
        })
      }
    } catch (error) {
      console.error('🔥 ADMIN: Error fetching platform config:', error)
      // Set fallback config
      setPlatformConfig({
        domain: 'sellzio.com',
        protocol: 'https',
        subdomainPattern: '*.sellzio.com'
      })
    }
  }

  const fetchTenants = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/tenants/list?orderBy=created_at&orderDirection=desc')

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch tenants')
      }

      setTenants(result.data || [])
    } catch (err) {
      console.error('Error fetching tenants:', err)
      toast({
        title: "Error",
        description: "Failed to fetch tenants data",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchPendingRequests = async () => {
    try {
      const response = await fetch('/api/admin/custom-domain-requests?status=pending')
      const data = await response.json()

      if (data.success) {
        setPendingRequests(data.requests || [])
      }
    } catch (error) {
      console.error('Error fetching pending requests:', error)
    }
  }

  const filteredTenants = tenants.filter(tenant =>
    tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    tenant.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (tenant.domain && tenant.domain.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  const handleSetDomain = async (tenantId: string, domain: string) => {
    if (!domain.trim()) {
      toast({
        title: "Error",
        description: "Please enter a domain name",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/tenants/lookup-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain: domain.trim(),
          tenantId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to set custom domain')
      }

      const data = await response.json()
      
      if (data.success) {
        await Promise.all([
          fetchTenants(),
          fetchPendingRequests()
        ])
        setNewDomain('')
        setSelectedTenant(null)
        toast({
          title: "Success",
          description: `Domain ${domain} has been set for tenant`,
        })
      }
    } catch (error) {
      console.error('Error setting domain:', error)
      toast({
        title: "Error",
        description: "Failed to set domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteDomain = async (tenantId: string) => {
    const confirmed = window.confirm(
      "Are you sure you want to remove the custom domain? The tenant will only be accessible via subdomain."
    )

    if (!confirmed) return

    setIsLoading(true)
    try {
      const response = await fetch('/api/tenants/lookup-domain', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tenantId })
      })

      if (!response.ok) {
        throw new Error('Failed to delete domain')
      }

      const data = await response.json()
      
      if (data.success) {
        await Promise.all([
          fetchTenants(),
          fetchPendingRequests()
        ])
        setDomainStatus(null) // Clear domain status
        toast({
          title: "Success",
          description: "Custom domain has been removed",
        })
      }
    } catch (error) {
      console.error('Error deleting domain:', error)
      toast({
        title: "Error",
        description: "Failed to delete domain. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleVerifyDomain = async (domain: string, tenantId: string) => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/tenants/verify-domain', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          domain,
          tenantId
        })
      })

      const data = await response.json()

      setDomainStatus({
        domain,
        verified: data.verified || false,
        ssl: data.ssl || false,
        lastChecked: new Date().toISOString()
      })

      toast({
        title: data.verified ? "Domain Verified" : "Verification Failed",
        description: data.verified ? "Domain is properly configured" : data.message || "Please check DNS settings",
        variant: data.verified ? "default" : "destructive"
      })
    } catch (error) {
      console.error('Error verifying domain:', error)
      toast({
        title: "Verification Failed",
        description: "Failed to verify domain",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied",
      description: "Copied to clipboard",
    })
  }

  const getCustomDomainStatus = (tenantId: string) => {
    const request = pendingRequests.find(req => req.tenants?.id === tenantId)
    return request?.status || null
  }

  const getStatusBadge = (status: string | null) => {
    if (!status) return null

    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-yellow-600"><Clock className="h-3 w-3 mr-1" />Pending Approval</Badge>
      case 'approved':
        return <Badge variant="default" className="text-green-600"><CheckCircle className="h-3 w-3 mr-1" />Approved</Badge>
      case 'rejected':
        return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />Rejected</Badge>
      case 'configuration_failed':
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Config Failed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-2 text-gray-600">Loading tenants...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Domain Management</h1>
          <p className="text-muted-foreground">
            Manage custom domains for all tenants in the system
          </p>
        </div>
        <Button
          variant="outline"
          onClick={() => {
            fetchTenants()
            fetchPendingRequests()
          }}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Pending Requests Alert */}
      {pendingRequests.length > 0 && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertTitle>Pending Custom Domain Requests</AlertTitle>
          <AlertDescription className="flex items-center justify-between">
            <span>
              You have {pendingRequests.length} pending custom domain request(s) awaiting approval.
            </span>
            <Link href="/admin/dashboard/custom-domain-requests">
              <Button
                variant="outline"
                size="sm"
                className="bg-orange-50 border-orange-200 text-orange-700 hover:bg-orange-100 hover:border-orange-300"
              >
                Review Requests
              </Button>
            </Link>
          </AlertDescription>
        </Alert>
      )}

      {/* Quick Domain Setup */}
      <QuickDomainSetup onDomainAdded={fetchTenants} />

      {/* Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Tenants
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Input
            placeholder="Search by tenant name, subdomain, or domain..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </CardContent>
      </Card>

      {/* Tenants List */}
      <Card>
        <CardHeader>
          <CardTitle>Tenants & Domains</CardTitle>
          <CardDescription>
            {filteredTenants.length} tenant(s) found
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredTenants.map((tenant) => (
              <div key={tenant.id} className="border rounded-lg p-4 space-y-3">
                {/* Tenant Info */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{tenant.name}</h3>
                    <p className="text-sm text-muted-foreground">ID: {tenant.id}</p>
                  </div>
                  <Badge variant={tenant.subscriptionStatus === 'active' ? 'default' : 'secondary'}>
                    {tenant.subscriptionPlan} - {tenant.subscriptionStatus}
                  </Badge>
                </div>

                {/* Domain Info */}
                <div className="grid gap-4 md:grid-cols-2">
                  {/* Subdomain */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Subdomain</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        value={`${tenant.slug}.${platformConfig?.domain || 'sellzio.com'}`}
                        readOnly
                        className="text-sm"
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard(`${tenant.slug}.${platformConfig?.domain || 'sellzio.com'}`)}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`${platformConfig?.protocol || 'https'}://${tenant.slug}.${platformConfig?.domain || 'sellzio.com'}`, '_blank')}
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>

                  {/* Custom Domain */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Custom Domain</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        value={tenant.domain || 'Not set'}
                        readOnly
                        className="text-sm"
                      />
                      {tenant.domain && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(tenant.domain!)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(`https://${tenant.domain}`, '_blank')}
                          >
                            <ExternalLink className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleVerifyDomain(tenant.domain!, tenant.id)}
                            disabled={isLoading}
                          >
                            <CheckCircle className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteDomain(tenant.id)}
                            disabled={isLoading}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSelectedTenant(tenant)}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Domain Status */}
                    {tenant.domain && domainStatus && domainStatus.domain === tenant.domain && (
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-sm text-muted-foreground">Verification Status:</span>
                        <Badge variant={domainStatus.verified ? "default" : "destructive"}>
                          {domainStatus.verified ? (
                            <>
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Verified
                            </>
                          ) : (
                            <>
                              <XCircle className="h-3 w-3 mr-1" />
                              Not Verified
                            </>
                          )}
                        </Badge>
                        {domainStatus.ssl && (
                          <Badge variant="default" className="bg-green-100 text-green-800">
                            <Shield className="h-3 w-3 mr-1" />
                            SSL Active
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Custom Domain Request Status */}
                    {getStatusBadge(getCustomDomainStatus(tenant.id)) && (
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-sm text-muted-foreground">Request Status:</span>
                        {getStatusBadge(getCustomDomainStatus(tenant.id))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Inline Edit Form - Show only for selected tenant */}
                {selectedTenant && selectedTenant.id === tenant.id && (
                  <div className="mt-4 p-4 border-t bg-gray-50 rounded-b-lg">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-lg">Set Custom Domain for {selectedTenant.name}</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedTenant(null)
                            setNewDomain('')
                          }}
                        >
                          ✕
                        </Button>
                      </div>

                      <div>
                        <Label htmlFor="domain">Domain Name</Label>
                        <div className="flex gap-2 mt-1">
                          <Input
                            id="domain"
                            placeholder="example.com"
                            value={newDomain}
                            onChange={(e) => setNewDomain(e.target.value)}
                            className="flex-1"
                          />
                          <Button
                            onClick={() => handleSetDomain(selectedTenant.id, newDomain)}
                            disabled={isLoading || !newDomain.trim()}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            {isLoading ? 'Setting...' : 'Set Domain'}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setSelectedTenant(null)
                              setNewDomain('')
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>

                      {/* DNS Instructions */}
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>DNS Configuration Required</AlertTitle>
                        <AlertDescription className="mt-2">
                          <p className="mb-3">Configure these DNS records in your domain provider:</p>
                          <div className="bg-white p-3 rounded text-sm font-mono border">
                            <div className="grid grid-cols-3 gap-2">
                              <div><strong>Type:</strong> CNAME</div>
                              <div><strong>Name:</strong> @</div>
                              <div><strong>Target:</strong> {selectedTenant.slug}.{platformConfig?.domain || 'sellzio.com'}</div>
                            </div>
                          </div>
                        </AlertDescription>
                      </Alert>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>


    </div>
  )
}
