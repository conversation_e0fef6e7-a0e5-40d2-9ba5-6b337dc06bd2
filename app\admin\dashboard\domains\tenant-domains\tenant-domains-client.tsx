"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PageHeader } from "@/components/admin/ui/page-header"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>eft, Loader2 } from "lucide-react"
import Link from "next/link"
import { DomainManagementList } from "@/components/admin/tenants/domain-management-list"

interface DomainStats {
  totalTenants: number
  totalDomains: number
  totalSubdomains: number
  verifiedDomains: number
  pendingDomains: number
  sslExpiring: number
  verificationRate: number
}

export function TenantDomainsClient() {
  const [stats, setStats] = useState<DomainStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/domains/stats')
      const data = await response.json()

      if (data.success) {
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching domain stats:', error)
    } finally {
      setLoading(false)
    }
  }
  return (
    <div className="flex flex-col gap-6">
      <PageHeader
        title="Tenant Domain Management"
        description="Manage tenant domains and SSL certificates"
        breadcrumbs={[
          { title: "Dashboard", href: "/admin/dashboard" },
          { title: "Domain Management", href: "/admin/dashboard/domains" },
          { title: "Tenant Domains", href: "/admin/dashboard/domains/tenant-domains" },
        ]}
        actions={
          <Link href="/admin/dashboard/domains">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Domain Management
            </Button>
          </Link>
        }
      />

      <div className="grid gap-6 md:grid-cols-3 lg:grid-cols-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Total Domains</CardTitle>
            <CardDescription>All registered domains</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Loader2 className="h-8 w-8 animate-spin" />
            ) : (
              <div className="text-3xl font-bold">{stats?.totalDomains || 0}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Verified</CardTitle>
            <CardDescription>Verified domains</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Loader2 className="h-8 w-8 animate-spin" />
            ) : (
              <>
                <div className="text-3xl font-bold">{stats?.verifiedDomains || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.verificationRate || 0}% of total domains
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Pending</CardTitle>
            <CardDescription>Pending verification</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Loader2 className="h-8 w-8 animate-spin" />
            ) : (
              <>
                <div className="text-3xl font-bold">{stats?.pendingDomains || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.totalDomains ? Math.round(((stats?.pendingDomains || 0) / stats.totalDomains) * 100) : 0}% of total domains
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>SSL Expiring</CardTitle>
            <CardDescription>Within 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Loader2 className="h-8 w-8 animate-spin" />
            ) : (
              <>
                <div className="text-3xl font-bold">{stats?.sslExpiring || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.totalDomains ? Math.round(((stats?.sslExpiring || 0) / stats.totalDomains) * 100) : 0}% of total domains
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Custom Domains</CardTitle>
            <CardDescription>Custom domain names</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Loader2 className="h-8 w-8 animate-spin" />
            ) : (
              <>
                <div className="text-3xl font-bold">{stats?.totalDomains || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.totalTenants ? Math.round(((stats?.totalDomains || 0) / stats.totalTenants) * 100) : 0}% of tenants
                </p>
              </>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Subdomains</CardTitle>
            <CardDescription>Sellzio subdomains</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <Loader2 className="h-8 w-8 animate-spin" />
            ) : (
              <>
                <div className="text-3xl font-bold">{stats?.totalSubdomains || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {stats?.totalTenants ? Math.round(((stats?.totalSubdomains || 0) / stats.totalTenants) * 100) : 0}% of tenants
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      <DomainManagementList />
    </div>
  )
}
