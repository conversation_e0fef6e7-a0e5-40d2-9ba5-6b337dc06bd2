import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching all tenant domains')

    const supabase = getClient()

    // Fetch all tenants with their domain information and verification status
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select(`
        id, name, domain, subdomain, status, created_at,
        custom_domain_request, custom_domain_status,
        custom_domain_requested_at, custom_domain_reviewed_at, custom_domain_reviewed_by,
        domain_verification_status, ssl_certificate_status, ssl_expires_at
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('🔥 API: Error fetching tenant domains:', error)
      return NextResponse.json(
        { error: 'Failed to fetch tenant domains' },
        { status: 500 }
      )
    }

    // Transform data to match the expected format
    const domains = tenants.map(tenant => {
      const hasCustomDomain = tenant.domain && tenant.domain !== `${tenant.subdomain}.sellzio.my.id`
      const domainName = hasCustomDomain ? tenant.domain : `${tenant.subdomain}.sellzio.my.id`

      // Check for custom domain requests
      const hasCustomDomainRequest = tenant.custom_domain_status && tenant.custom_domain_status !== 'none'

      // Determine status based on domain verification status and custom domain status
      let status = 'active' // Default for subdomains
      if (hasCustomDomain) {
        status = tenant.domain_verification_status || 'pending'
      } else if (hasCustomDomainRequest) {
        // Show custom domain request status for tenants without approved domains
        status = tenant.custom_domain_status === 'pending' ? 'pending' : 'active'
      }

      // SSL status from database
      const sslStatus = tenant.ssl_certificate_status || 'pending'

      // SSL expiry calculation
      const sslExpiry = tenant.ssl_expires_at ? new Date(tenant.ssl_expires_at).toISOString().split('T')[0] : null

      return {
        id: tenant.id,
        domain: domainName,
        subdomain: tenant.subdomain,
        tenantName: tenant.name,
        tenantId: tenant.id,
        status: status,
        sslStatus: sslStatus,
        sslExpiry: sslExpiry,
        createdAt: tenant.created_at,
        isCustomDomain: hasCustomDomain,
        customDomainRequest: hasCustomDomainRequest ? {
          domain: tenant.custom_domain_request,
          status: tenant.custom_domain_status,
          requestedAt: tenant.custom_domain_requested_at
        } : null,
        hasCustomDomainRequest
      }
    })

    console.log('🔥 API: Successfully fetched', domains.length, 'domains')

    return NextResponse.json({
      success: true,
      domains
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, tenantId, domain } = await request.json()

    console.log('🔥 API: Processing custom domain request:', { action, tenantId, domain })

    const supabase = getClient()

    if (action === 'approve') {
      // Update the tenant's custom domain status and domain
      const { error: updateError } = await supabase
        .from('tenants')
        .update({
          domain: domain,
          custom_domain_status: 'approved',
          custom_domain_reviewed_at: new Date().toISOString(),
          custom_domain_reviewed_by: 'admin'
        })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 API: Error updating tenant:', updateError)
        return NextResponse.json(
          { error: 'Failed to approve custom domain' },
          { status: 500 }
        )
      }

      console.log('🔥 API: Custom domain approved successfully')
      return NextResponse.json({
        success: true,
        message: 'Custom domain approved successfully'
      })

    } else if (action === 'reject') {
      // Update the tenant's custom domain status
      const { error: updateError } = await supabase
        .from('tenants')
        .update({
          custom_domain_status: 'rejected',
          custom_domain_reviewed_at: new Date().toISOString(),
          custom_domain_reviewed_by: 'admin'
        })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 API: Error updating tenant:', updateError)
        return NextResponse.json(
          { error: 'Failed to reject custom domain' },
          { status: 500 }
        )
      }

      console.log('🔥 API: Custom domain rejected successfully')
      return NextResponse.json({
        success: true,
        message: 'Custom domain rejected successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
