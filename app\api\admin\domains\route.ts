import { NextRequest, NextResponse } from 'next/server'
import { getClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching all tenant domains')

    const supabase = getClient()

    // Fetch all tenants with their domain information
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select('id, name, domain, subdomain, status, created_at')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('🔥 API: Error fetching tenant domains:', error)
      return NextResponse.json(
        { error: 'Failed to fetch tenant domains' },
        { status: 500 }
      )
    }

    // Transform data to match the expected format
    const domains = tenants.map(tenant => {
      const hasCustomDomain = tenant.domain && tenant.domain !== `${tenant.subdomain}.sellzio.my.id`
      const domainName = hasCustomDomain ? tenant.domain : `${tenant.subdomain}.sellzio.my.id`
      
      return {
        id: tenant.id,
        domain: domainName,
        tenantName: tenant.name,
        tenantId: tenant.id,
        status: hasCustomDomain ? 'verified' : 'active', // Assume subdomain is always active
        sslStatus: hasCustomDomain ? 'active' : 'active', // Assume SSL is active for now
        sslExpiry: hasCustomDomain ? '2024-12-31' : null, // Mock SSL expiry for custom domains
        createdAt: tenant.created_at,
        isCustomDomain: hasCustomDomain,
        subdomain: tenant.subdomain
      }
    })

    console.log('🔥 API: Successfully fetched', domains.length, 'domains')

    return NextResponse.json({
      success: true,
      domains
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
