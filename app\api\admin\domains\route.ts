import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching all tenant domains')

    const supabase = getClient()

    // Fetch all tenants with their domain information and verification status
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select(`
        id, name, domain, subdomain, status, created_at,
        custom_domain_request, custom_domain_status,
        custom_domain_requested_at, custom_domain_reviewed_at, custom_domain_reviewed_by,
        domain_verification_status, ssl_certificate_status, ssl_expires_at
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('🔥 API: Error fetching tenant domains:', error)
      return NextResponse.json(
        { error: 'Failed to fetch tenant domains' },
        { status: 500 }
      )
    }

    // Transform data to match the expected format
    const domains = tenants.map(tenant => {
      const hasCustomDomain = tenant.domain && tenant.domain !== `${tenant.subdomain}.sellzio.my.id`
      const domainName = hasCustomDomain ? tenant.domain : `${tenant.subdomain}.sellzio.my.id`

      // Check for custom domain requests
      const hasCustomDomainRequest = tenant.custom_domain_status && tenant.custom_domain_status !== 'none'

      // Determine status based on domain verification status and custom domain status
      let status = 'active' // Default for subdomains
      if (hasCustomDomain) {
        status = tenant.domain_verification_status || 'pending'
      } else if (hasCustomDomainRequest) {
        // Show custom domain request status for tenants without approved domains
        status = tenant.custom_domain_status === 'pending' ? 'pending' : 'active'
      }

      // SSL status from database
      const sslStatus = tenant.ssl_certificate_status || 'pending'

      // SSL expiry calculation
      const sslExpiry = tenant.ssl_expires_at ? new Date(tenant.ssl_expires_at).toISOString().split('T')[0] : null

      return {
        id: tenant.id,
        domain: domainName,
        subdomain: tenant.subdomain,
        tenantName: tenant.name,
        tenantId: tenant.id,
        status: status,
        sslStatus: sslStatus,
        sslExpiry: sslExpiry,
        createdAt: tenant.created_at,
        isCustomDomain: hasCustomDomain,
        customDomainRequest: hasCustomDomainRequest ? {
          domain: tenant.custom_domain_request,
          status: tenant.custom_domain_status,
          requestedAt: tenant.custom_domain_requested_at
        } : null,
        hasCustomDomainRequest
      }
    })

    console.log('🔥 API: Successfully fetched', domains.length, 'domains')

    return NextResponse.json({
      success: true,
      domains
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, tenantId, domain } = await request.json()

    console.log('🔥 API: Processing custom domain request:', { action, tenantId, domain })

    const supabase = getClient()

    if (action === 'approve') {
      console.log('🔥 API: Starting custom domain approval process for:', domain)

      try {
        // Step 1: Add domain to Cloudflare DNS
        console.log('🔥 API: Adding domain to Cloudflare DNS:', domain)
        const cloudflareResponse = await fetch(`https://api.cloudflare.com/client/v4/zones/${process.env.CLOUDFLARE_ZONE_ID}/dns_records`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type: 'CNAME',
            name: domain,
            content: 'cname.vercel-dns.com',
            proxied: false // DNS only mode for Vercel compatibility
          })
        })

        const cloudflareData = await cloudflareResponse.json()

        if (!cloudflareResponse.ok) {
          console.error('🔥 API: Cloudflare error:', cloudflareData)
          // Continue even if Cloudflare fails (domain might already exist)
        } else {
          console.log('🔥 API: Domain added to Cloudflare DNS successfully')
        }

        // Step 2: Add domain to Vercel
        console.log('🔥 API: Adding domain to Vercel:', domain)
        const vercelResponse = await fetch(`https://api.vercel.com/v10/projects/${process.env.VERCEL_PROJECT_ID}/domains`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.VERCEL_TOKEN}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: domain
          })
        })

        const vercelData = await vercelResponse.json()

        if (!vercelResponse.ok) {
          console.error('🔥 API: Vercel error:', vercelData)
          // Continue even if Vercel fails (domain might already exist)
        } else {
          console.log('🔥 API: Domain added to Vercel successfully')
        }

        // Step 3: Update the tenant's custom domain status and domain
        const { error: updateError } = await supabase
          .from('tenants')
          .update({
            domain: domain,
            custom_domain_status: 'approved',
            custom_domain_reviewed_at: new Date().toISOString(),
            custom_domain_reviewed_by: 'admin'
          })
          .eq('id', tenantId)

        if (updateError) {
          console.error('🔥 API: Error updating tenant:', updateError)
          return NextResponse.json(
            { error: 'Failed to approve custom domain' },
            { status: 500 }
          )
        }

        console.log('🔥 API: Custom domain approved and configured successfully')
        return NextResponse.json({
          success: true,
          message: 'Custom domain approved and automatically configured in Cloudflare and Vercel',
          cloudflare: cloudflareResponse.ok ? 'success' : 'failed',
          vercel: vercelResponse.ok ? 'success' : 'failed'
        })

      } catch (integrationError) {
        console.error('🔥 API: Error during domain integration:', integrationError)

        // Still update database even if integration fails
        const { error: updateError } = await supabase
          .from('tenants')
          .update({
            domain: domain,
            custom_domain_status: 'approved',
            custom_domain_reviewed_at: new Date().toISOString(),
            custom_domain_reviewed_by: 'admin'
          })
          .eq('id', tenantId)

        if (updateError) {
          console.error('🔥 API: Error updating tenant after integration failure:', updateError)
          return NextResponse.json(
            { error: 'Failed to approve custom domain' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Custom domain approved but automatic configuration failed. Please configure manually.',
          warning: 'Manual configuration required'
        })
      }

    } else if (action === 'reject') {
      // Update the tenant's custom domain status
      const { error: updateError } = await supabase
        .from('tenants')
        .update({
          custom_domain_status: 'rejected',
          custom_domain_reviewed_at: new Date().toISOString(),
          custom_domain_reviewed_by: 'admin'
        })
        .eq('id', tenantId)

      if (updateError) {
        console.error('🔥 API: Error updating tenant:', updateError)
        return NextResponse.json(
          { error: 'Failed to reject custom domain' },
          { status: 500 }
        )
      }

      console.log('🔥 API: Custom domain rejected successfully')
      return NextResponse.json({
        success: true,
        message: 'Custom domain rejected successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
