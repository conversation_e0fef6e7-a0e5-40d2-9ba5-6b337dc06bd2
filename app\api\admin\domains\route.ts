import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching all tenant domains')

    const supabase = getClient()

    // Fetch all tenants with their domain information and custom domain requests
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select(`
        id, name, domain, subdomain, status, created_at,
        custom_domain_requests (
          id, domain, status, requested_at, reviewed_at, reviewed_by, notes
        )
      `)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('🔥 API: Error fetching tenant domains:', error)
      return NextResponse.json(
        { error: 'Failed to fetch tenant domains' },
        { status: 500 }
      )
    }

    // Transform data to match the expected format
    const domains = tenants.map(tenant => {
      const hasCustomDomain = tenant.domain && tenant.domain !== `${tenant.subdomain}.sellzio.my.id`
      const domainName = hasCustomDomain ? tenant.domain : `${tenant.subdomain}.sellzio.my.id`

      // Check for pending custom domain requests
      const pendingRequest = tenant.custom_domain_requests?.find(req => req.status === 'pending')
      const hasCustomDomainRequest = tenant.custom_domain_requests && tenant.custom_domain_requests.length > 0

      return {
        id: tenant.id,
        domain: domainName,
        subdomain: tenant.subdomain,
        tenantName: tenant.name,
        tenantId: tenant.id,
        status: hasCustomDomain ? 'verified' : 'active', // Assume subdomain is always active
        sslStatus: hasCustomDomain ? 'active' : 'active', // Assume SSL is active for now
        sslExpiry: hasCustomDomain ? '2024-12-31' : null, // Mock SSL expiry for custom domains
        createdAt: tenant.created_at,
        isCustomDomain: hasCustomDomain,
        customDomainRequest: pendingRequest ? {
          id: pendingRequest.id,
          domain: pendingRequest.domain,
          status: pendingRequest.status,
          requestedAt: pendingRequest.requested_at
        } : null,
        hasCustomDomainRequest
      }
    })

    console.log('🔥 API: Successfully fetched', domains.length, 'domains')

    return NextResponse.json({
      success: true,
      domains
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, requestId, tenantId, domain } = await request.json()

    console.log('🔥 API: Processing custom domain request:', { action, requestId, tenantId, domain })

    const supabase = getClient()

    if (action === 'approve') {
      // Update the custom domain request status
      const { error: updateError } = await supabase
        .from('custom_domain_requests')
        .update({
          status: 'approved',
          reviewed_at: new Date().toISOString(),
          reviewed_by: 'admin'
        })
        .eq('id', requestId)

      if (updateError) {
        console.error('🔥 API: Error updating request status:', updateError)
        return NextResponse.json(
          { error: 'Failed to update request status' },
          { status: 500 }
        )
      }

      // Update the tenant's domain
      const { error: tenantError } = await supabase
        .from('tenants')
        .update({ domain })
        .eq('id', tenantId)

      if (tenantError) {
        console.error('🔥 API: Error updating tenant domain:', tenantError)
        return NextResponse.json(
          { error: 'Failed to update tenant domain' },
          { status: 500 }
        )
      }

      console.log('🔥 API: Custom domain approved successfully')
      return NextResponse.json({
        success: true,
        message: 'Custom domain approved successfully'
      })

    } else if (action === 'reject') {
      // Update the custom domain request status
      const { error: updateError } = await supabase
        .from('custom_domain_requests')
        .update({
          status: 'rejected',
          reviewed_at: new Date().toISOString(),
          reviewed_by: 'admin'
        })
        .eq('id', requestId)

      if (updateError) {
        console.error('🔥 API: Error updating request status:', updateError)
        return NextResponse.json(
          { error: 'Failed to update request status' },
          { status: 500 }
        )
      }

      console.log('🔥 API: Custom domain rejected successfully')
      return NextResponse.json({
        success: true,
        message: 'Custom domain rejected successfully'
      })
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
