import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

function getClient() {
  return createClient(supabaseUrl, supabaseServiceKey)
}

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching domain statistics')

    const supabase = getClient()

    // Fetch all tenants with their domain information
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select('id, name, domain, subdomain, status, created_at')

    if (error) {
      console.error('🔥 API: Error fetching tenant domains for stats:', error)
      return NextResponse.json(
        { error: 'Failed to fetch domain statistics' },
        { status: 500 }
      )
    }

    // Calculate statistics
    const totalDomains = tenants.length

    // Count custom domains: tenants that have a domain that is NOT null and NOT the default subdomain format
    const customDomains = tenants.filter(tenant =>
      tenant.domain && tenant.domain !== `${tenant.subdomain}.sellzio.my.id`
    ).length

    // Count subdomains: tenants that either have no domain or have the default subdomain format
    const subdomains = tenants.filter(tenant =>
      !tenant.domain || tenant.domain === `${tenant.subdomain}.sellzio.my.id`
    ).length
    
    // For now, assume all domains are verified (in real implementation, you'd check DNS/SSL status)
    const verifiedDomains = totalDomains
    const pendingDomains = 0
    const sslExpiring = 0 // Would need to check actual SSL certificates

    const stats = {
      totalDomains,
      verifiedDomains,
      pendingDomains,
      sslExpiring,
      customDomains,
      subdomains,
      verificationRate: totalDomains > 0 ? Math.round((verifiedDomains / totalDomains) * 100) : 0
    }

    console.log('🔥 API: Domain statistics:', stats)

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
