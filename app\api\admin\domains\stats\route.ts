import { NextRequest, NextResponse } from 'next/server'
import { getClient } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 API: Fetching domain statistics')

    const supabase = getClient()

    // Fetch all tenants with their domain information
    const { data: tenants, error } = await supabase
      .from('tenants')
      .select('id, name, domain, subdomain, status, created_at')

    if (error) {
      console.error('🔥 API: Error fetching tenant domains for stats:', error)
      return NextResponse.json(
        { error: 'Failed to fetch domain statistics' },
        { status: 500 }
      )
    }

    // Calculate statistics
    const totalDomains = tenants.length
    const customDomains = tenants.filter(tenant => 
      tenant.domain && tenant.domain !== `${tenant.subdomain}.sellzio.my.id`
    ).length
    const subdomains = totalDomains - customDomains
    
    // For now, assume all domains are verified (in real implementation, you'd check DNS/SSL status)
    const verifiedDomains = totalDomains
    const pendingDomains = 0
    const sslExpiring = 0 // Would need to check actual SSL certificates

    const stats = {
      totalDomains,
      verifiedDomains,
      pendingDomains,
      sslExpiring,
      customDomains,
      subdomains,
      verificationRate: totalDomains > 0 ? Math.round((verifiedDomains / totalDomains) * 100) : 0
    }

    console.log('🔥 API: Domain statistics:', stats)

    return NextResponse.json({
      success: true,
      stats
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
