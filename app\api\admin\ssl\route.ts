import { NextRequest, NextResponse } from 'next/server'
import { getClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const expiring = searchParams.get('expiring') === 'true'
    const status = searchParams.get('status')

    console.log('🔥 API: Fetching SSL certificates', { expiring, status })

    const supabase = getClient()

    let query = supabase
      .from('ssl_certificates')
      .select(`
        id, domain, certificate_type, status, 
        issued_at, expires_at, auto_renew,
        tenant_id,
        tenants!inner(name, subdomain)
      `)

    if (expiring) {
      const thirtyDaysFromNow = new Date()
      thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30)
      query = query
        .eq('status', 'active')
        .lte('expires_at', thirtyDaysFromNow.toISOString())
    }

    if (status) {
      query = query.eq('status', status)
    }

    const { data: certificates, error } = await query.order('expires_at', { ascending: true })

    if (error) {
      console.error('🔥 API: Error fetching SSL certificates:', error)
      return NextResponse.json(
        { error: 'Failed to fetch SSL certificates' },
        { status: 500 }
      )
    }

    console.log(`🔥 API: Successfully fetched ${certificates.length} SSL certificates`)

    return NextResponse.json({
      success: true,
      certificates: certificates.map(cert => ({
        id: cert.id,
        domain: cert.domain,
        tenantName: cert.tenants.name,
        certificateType: cert.certificate_type,
        status: cert.status,
        issuedAt: cert.issued_at,
        expiresAt: cert.expires_at,
        autoRenew: cert.auto_renew,
        daysUntilExpiry: cert.expires_at ? 
          Math.ceil((new Date(cert.expires_at).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)) : 
          null
      }))
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { tenantId, domain, certificateType = 'letsencrypt' } = await request.json()

    if (!tenantId || !domain) {
      return NextResponse.json(
        { error: 'Tenant ID and domain are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: Issuing SSL certificate:', { tenantId, domain, certificateType })

    const supabase = getClient()

    // Simulate certificate issuance (in real implementation, you'd use Let's Encrypt or other CA)
    const issuedAt = new Date()
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 90) // 90 days validity

    const { data: certificate, error } = await supabase
      .from('ssl_certificates')
      .upsert({
        tenant_id: tenantId,
        domain,
        certificate_type: certificateType,
        status: 'active',
        issued_at: issuedAt.toISOString(),
        expires_at: expiresAt.toISOString(),
        auto_renew: true,
        certificate_data: {
          issuer: 'Let\'s Encrypt',
          algorithm: 'RSA-2048',
          fingerprint: `sha256:${Math.random().toString(36).substring(2)}`
        }
      }, {
        onConflict: 'tenant_id,domain'
      })
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error issuing SSL certificate:', error)
      return NextResponse.json(
        { error: 'Failed to issue SSL certificate' },
        { status: 500 }
      )
    }

    console.log('🔥 API: SSL certificate issued successfully')

    return NextResponse.json({
      success: true,
      certificate: {
        id: certificate.id,
        domain: certificate.domain,
        status: certificate.status,
        issuedAt: certificate.issued_at,
        expiresAt: certificate.expires_at,
        certificateType: certificate.certificate_type
      }
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error in SSL certificate issuance:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { certificateId, action } = await request.json()

    if (!certificateId || !action) {
      return NextResponse.json(
        { error: 'Certificate ID and action are required' },
        { status: 400 }
      )
    }

    console.log('🔥 API: SSL certificate action:', { certificateId, action })

    const supabase = getClient()

    let updateData: any = {}

    switch (action) {
      case 'renew':
        const newExpiresAt = new Date()
        newExpiresAt.setDate(newExpiresAt.getDate() + 90)
        updateData = {
          status: 'active',
          issued_at: new Date().toISOString(),
          expires_at: newExpiresAt.toISOString(),
          updated_at: new Date().toISOString()
        }
        break
      case 'revoke':
        updateData = {
          status: 'revoked',
          updated_at: new Date().toISOString()
        }
        break
      case 'toggle_auto_renew':
        // Get current auto_renew status first
        const { data: currentCert } = await supabase
          .from('ssl_certificates')
          .select('auto_renew')
          .eq('id', certificateId)
          .single()
        
        updateData = {
          auto_renew: !currentCert?.auto_renew,
          updated_at: new Date().toISOString()
        }
        break
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    const { data: certificate, error } = await supabase
      .from('ssl_certificates')
      .update(updateData)
      .eq('id', certificateId)
      .select()
      .single()

    if (error) {
      console.error('🔥 API: Error updating SSL certificate:', error)
      return NextResponse.json(
        { error: 'Failed to update SSL certificate' },
        { status: 500 }
      )
    }

    console.log('🔥 API: SSL certificate updated successfully')

    return NextResponse.json({
      success: true,
      certificate,
      message: `SSL certificate ${action} completed successfully`
    })

  } catch (error) {
    console.error('🔥 API: Unexpected error in SSL certificate update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
