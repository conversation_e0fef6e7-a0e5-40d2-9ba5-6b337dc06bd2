"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useNotifications } from "@/components/providers/notifications-provider"
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Search,
  Plus,
  MoreHorizontal,
  ExternalLink,
  RefreshCw,
  Shield,
  Trash2,
  Loader2,
} from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Domain {
  id: string
  domain: string
  subdomain: string
  tenantName: string
  tenantId: string
  status: string
  sslStatus: string
  sslExpiry: string | null
  createdAt: string
  isCustomDomain: boolean
  customDomainRequest: {
    domain: string
    status: string
    requestedAt: string
  } | null
  hasCustomDomainRequest: boolean
}

export function DomainManagementList() {
  const [searchQuery, setSearchQuery] = useState("")
  const [domains, setDomains] = useState<Domain[]>([])
  const [loading, setLoading] = useState(true)
  const { showNotification } = useNotifications()

  // Fetch domains from API
  useEffect(() => {
    fetchDomains()
  }, [])

  const fetchDomains = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/domains')
      const data = await response.json()

      if (data.success) {
        setDomains(data.domains)
      } else {
        showNotification("Failed to fetch domains", "error")
      }
    } catch (error) {
      console.error('Error fetching domains:', error)
      showNotification("Error fetching domains", "error")
    } finally {
      setLoading(false)
    }
  }

  const filteredDomains = domains.filter(
    (domain) =>
      domain.domain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      domain.subdomain.toLowerCase().includes(searchQuery.toLowerCase()) ||
      domain.tenantName.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const handleVerifyDomain = (domainId: string) => {
    // Simulate API call
    setTimeout(() => {
      showNotification("Domain verification initiated. This may take a few minutes.", "info")
    }, 500)
  }

  const handleRenewSSL = (domainId: string) => {
    // Simulate API call
    setTimeout(() => {
      showNotification("SSL certificate renewal initiated.", "success")
    }, 500)
  }

  const handleDeleteDomain = (domainId: string) => {
    // Simulate API call
    setTimeout(() => {
      showNotification("Domain deleted successfully.", "success")
    }, 500)
  }

  const handleApproveCustomDomain = async (tenantId: string, domain: string) => {
    try {
      const response = await fetch('/api/admin/domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve',
          tenantId,
          domain
        })
      })

      const data = await response.json()

      if (data.success) {
        showNotification("Custom domain approved successfully.", "success")
        fetchDomains() // Refresh the list
      } else {
        showNotification("Failed to approve custom domain.", "error")
      }
    } catch (error) {
      console.error('Error approving custom domain:', error)
      showNotification("Error approving custom domain.", "error")
    }
  }

  const handleRejectCustomDomain = async (tenantId: string) => {
    try {
      const response = await fetch('/api/admin/domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reject',
          tenantId
        })
      })

      const data = await response.json()

      if (data.success) {
        showNotification("Custom domain rejected successfully.", "success")
        fetchDomains() // Refresh the list
      } else {
        showNotification("Failed to reject custom domain.", "error")
      }
    } catch (error) {
      console.error('Error rejecting custom domain:', error)
      showNotification("Error rejecting custom domain.", "error")
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search domains..."
              className="pl-8"
              disabled
            />
          </div>
          <Button disabled>
            <Plus className="mr-2 h-4 w-4" />
            Add Domain
          </Button>
        </div>
        <div className="rounded-md border p-8 text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-muted-foreground" />
          <p className="mt-2 text-muted-foreground">Loading domains...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search domains..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button onClick={fetchDomains}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Subdomain</TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Tenant</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>SSL</TableHead>
              <TableHead>Custom Domain Request</TableHead>
              <TableHead>Created</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredDomains.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  {searchQuery ? "No domains found matching your search." : "No domains found."}
                </TableCell>
              </TableRow>
            ) : (
              filteredDomains.map((domain) => (
              <TableRow key={domain.id}>
                <TableCell className="font-medium">{domain.subdomain}.sellzio.my.id</TableCell>
                <TableCell className="font-medium">{domain.domain}</TableCell>
                <TableCell>{domain.tenantName}</TableCell>
                <TableCell>
                  {domain.status === "verified" ? (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Verified
                    </Badge>
                  ) : domain.status === "pending" ? (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-100 text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Failed
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {domain.sslStatus === "active" ? (
                    <Badge className="bg-green-100 text-green-800">
                      <Shield className="mr-1 h-3 w-3" />
                      Active
                    </Badge>
                  ) : domain.sslStatus === "expiring" ? (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                      <AlertTriangle className="mr-1 h-3 w-3" />
                      Expiring
                    </Badge>
                  ) : domain.sslStatus === "pending" ? (
                    <Badge variant="outline" className="bg-blue-100 text-blue-800">
                      <RefreshCw className="mr-1 h-3 w-3" />
                      Pending
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-red-100 text-red-800">
                      <XCircle className="mr-1 h-3 w-3" />
                      Failed
                    </Badge>
                  )}
                </TableCell>
                <TableCell>
                  {domain.customDomainRequest ? (
                    <div className="space-y-1">
                      <div className="text-sm font-medium">{domain.customDomainRequest.domain}</div>
                      <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                        <AlertTriangle className="mr-1 h-3 w-3" />
                        {domain.customDomainRequest.status}
                      </Badge>
                      <div className="text-xs text-muted-foreground">
                        {new Date(domain.customDomainRequest.requestedAt).toLocaleDateString()}
                      </div>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>{new Date(domain.createdAt).toLocaleDateString()}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => window.open(`https://${domain.domain}`, "_blank")}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Visit Domain
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => window.open(`https://${domain.subdomain}.sellzio.my.id`, "_blank")}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Lihat Subdomain
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {domain.status !== "verified" && (
                        <DropdownMenuItem onClick={() => handleVerifyDomain(domain.id)}>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Verify Domain
                        </DropdownMenuItem>
                      )}
                      {(domain.sslStatus === "expiring" || domain.sslStatus === "failed") && (
                        <DropdownMenuItem onClick={() => handleRenewSSL(domain.id)}>
                          <RefreshCw className="mr-2 h-4 w-4" />
                          Renew SSL
                        </DropdownMenuItem>
                      )}
                      {domain.customDomainRequest && domain.customDomainRequest.status === 'pending' && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="text-green-600"
                            onClick={() => handleApproveCustomDomain(
                              domain.tenantId,
                              domain.customDomainRequest!.domain
                            )}
                          >
                            <CheckCircle className="mr-2 h-4 w-4" />
                            Approve Custom Domain
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleRejectCustomDomain(domain.tenantId)}
                          >
                            <XCircle className="mr-2 h-4 w-4" />
                            Reject Custom Domain
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteDomain(domain.id)}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Domain
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
